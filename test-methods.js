// Script para probar los métodos de la API
const axios = require('axios');

// Configuración
const API_URL = 'http://localhost:3003/api/v1';
const USER_ID = '12345';
const headers = { 'x-user-id': USER_ID };

// Función para imprimir resultados
const printResult = (name, success, data = null, error = null) => {
  console.log('\n-----------------------------------');
  if (success) {
    console.log(`✅ ${name}: OK`);
    if (data) console.log(JSON.stringify(data, null, 2));
  } else {
    console.log(`❌ ${name}: FAILED`);
    if (error) console.log(error);
  }
  console.log('-----------------------------------');
};

// Funciones de prueba para cada método
async function testGetCart() {
  console.log('\n🛒 PROBANDO: Obtener carrito');
  try {
    const response = await axios.get(`${API_URL}/cart`, { headers });
    printResult('GET /cart', true, response.data);
    return response.data;
  } catch (error) {
    printResult('GET /cart', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testAddToCart() {
  console.log('\n➕ PROBANDO: Agregar producto al carrito');
  const productToAdd = {
    productId: 'prod123',
    storeId: 'store456',
    name: 'Producto de prueba',
    price: 19.99,
    quantity: 2
  };
  
  try {
    const response = await axios.post(`${API_URL}/cart/items`, productToAdd, { headers });
    printResult('POST /cart/items', true, response.data);
    return response.data;
  } catch (error) {
    printResult('POST /cart/items', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testUpdateCartItem() {
  console.log('\n🔄 PROBANDO: Actualizar cantidad de producto');
  try {
    const response = await axios.patch(
      `${API_URL}/cart/items/prod123`, 
      { quantity: 3 }, 
      { headers }
    );
    printResult('PATCH /cart/items/{id}', true, response.data);
    return response.data;
  } catch (error) {
    printResult('PATCH /cart/items/{id}', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testRemoveCartItem() {
  console.log('\n❌ PROBANDO: Eliminar producto del carrito');
  try {
    const response = await axios.delete(`${API_URL}/cart/items/prod123`, { headers });
    printResult('DELETE /cart/items/{id}', true, response.data);
    return response.data;
  } catch (error) {
    printResult('DELETE /cart/items/{id}', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testCreateOrder() {
  console.log('\n📦 PROBANDO: Crear pedido');
  try {
    // Primero agregamos un producto al carrito
    await testAddToCart();
    
    // Luego creamos el pedido
    const orderData = {
      fromCart: true,
      customer: {
        name: "Juan Pérez",
        phone: "+1234567890",
        email: "<EMAIL>",
        address: "Calle Principal 123"
      },
      deliveryMethod: "DELIVERY",
      paymentMethod: "tarjeta_credito"
    };
    
    const response = await axios.post(`${API_URL}/orders`, orderData, { headers });
    printResult('POST /orders', true, response.data);
    return response.data;
  } catch (error) {
    printResult('POST /orders', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testGetOrders() {
  console.log('\n📋 PROBANDO: Listar pedidos');
  try {
    const response = await axios.get(`${API_URL}/orders`, { headers });
    printResult('GET /orders', true, response.data);
    return response.data;
  } catch (error) {
    printResult('GET /orders', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testGetOrderById(orderId) {
  console.log(`\n🔍 PROBANDO: Obtener pedido por ID (${orderId})`);
  try {
    const response = await axios.get(`${API_URL}/orders/${orderId}`, { headers });
    printResult(`GET /orders/${orderId}`, true, response.data);
    return response.data;
  } catch (error) {
    printResult(`GET /orders/${orderId}`, false, null, error.response?.data || error.message);
    return null;
  }
}

async function testCancelOrder(orderId) {
  console.log(`\n❌ PROBANDO: Cancelar pedido (${orderId})`);
  try {
    const response = await axios.patch(`${API_URL}/orders/${orderId}/cancel`, {}, { headers });
    printResult(`PATCH /orders/${orderId}/cancel`, true, response.data);
    return response.data;
  } catch (error) {
    printResult(`PATCH /orders/${orderId}/cancel`, false, null, error.response?.data || error.message);
    return null;
  }
}

async function testGetOrderStats() {
  console.log('\n📊 PROBANDO: Obtener estadísticas de pedidos');
  try {
    const response = await axios.get(`${API_URL}/orders/stats`, { headers });
    printResult('GET /orders/stats', true, response.data);
    return response.data;
  } catch (error) {
    printResult('GET /orders/stats', false, null, error.response?.data || error.message);
    return null;
  }
}

async function testAssignDelivery(orderId) {
  console.log(`\n🚚 PROBANDO: Asignar repartidor al pedido (${orderId})`);
  const deliveryId = 'delivery123'; // ID del repartidor de prueba
  const deliveryHeaders = { 'x-user-id': deliveryId };
  
  try {
    const response = await axios.patch(
      `${API_URL}/orders/${orderId}/assign-delivery`, 
      {}, 
      { headers: deliveryHeaders }
    );
    printResult(`PATCH /orders/${orderId}/assign-delivery`, true, response.data);
    return response.data;
  } catch (error) {
    printResult(`PATCH /orders/${orderId}/assign-delivery`, false, null, error.response?.data || error.message);
    return null;
  }
}

// Función principal que ejecuta todas las pruebas
async function runAllTests() {
  console.log('🚀 INICIANDO PRUEBAS DE TODOS LOS MÉTODOS');
  console.log('===================================');
  
  try {
    // Pruebas de carrito
    await testGetCart();
    await testAddToCart();
    await testUpdateCartItem();
    await testRemoveCartItem();
    
    // Pruebas de pedidos
    const orderResponse = await testCreateOrder();
    let orderId = null;
    
    if (orderResponse && orderResponse.id) {
      orderId = orderResponse.id;
      await testGetOrderById(orderId);
      await testAssignDelivery(orderId);
      await testCancelOrder(orderId);
    }
    
    await testGetOrders();
    await testGetOrderStats();
    
    console.log('\n✨ TODAS LAS PRUEBAS COMPLETADAS');
  } catch (error) {
    console.log('\n❌ ERROR GENERAL:', error.message);
  }
}

// Ejecutar todas las pruebas
runAllTests();
