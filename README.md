# Purchasing Microservice - Carrito y Pedidos API

Microservicio desarrollado con NestJS, TypeScript y MongoDB para la gestión completa del proceso de compras, incluyendo carritos de compra y procesamiento de pedidos.

## 📋 Descripción

Este microservicio se enfoca en la gestión completa del proceso de compras, desde la administración del carrito de compras hasta el procesamiento de pedidos. Está diseñado para integrarse con otros microservicios como store-microservice y delivery-microservice. Proporciona una API RESTful completa para gestionar carritos, crear pedidos y obtener estadísticas.

## 🚀 Características

- **Framework**: NestJS con TypeScript
- **Base de datos**: MongoDB con Mongoose
- **Documentación**: Swagger/OpenAPI
- **Validación**: class-validator y class-transformer
- **Arquitectura**: Principios SOLID y mejores prácticas de NestJS

## 📦 Funcionalidades

### Carrito de Compras
- ✅ Gestión de carrito por usuario
- ✅ Agregar/quitar productos del carrito
- ✅ Actualizar cantidades de productos
- ✅ Validación de productos de diferentes tiendas
- ✅ Cálculo automático de totales
- ✅ Persistencia del carrito por usuario
- ✅ TTL para carritos abandonados

### Pedidos
- ✅ Crear pedidos desde el carrito
- ✅ Listar pedidos con filtros y paginación
- ✅ Ver detalles de pedidos específicos
- ✅ Cancelar pedidos (solo en estado pendiente)
- ✅ Obtener estadísticas de pedidos del cliente

### Generales
- ✅ Identificación de usuario por header x-user-id
- ✅ Manejo de errores global
- ✅ Logging de requests
- ✅ Documentación completa con Swagger
- ✅ Comunicación con otros microservicios

## 🛠️ Instalación

```bash
# Clonar el repositorio
git clone <repository-url>
cd purchasing-microservice

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones
```

## ⚙️ Configuración

### Variables de Entorno

```env
# Application Configuration
PORT=3000
NODE_ENV=development
API_PREFIX=api/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/purchasing-microservice

# Microservices URLs
STORE_MICROSERVICE_URL=http://localhost:3001
DELIVERY_MICROSERVICE_URL=http://localhost:3002

# Puerto del Purchasing Microservice
PORT=3003
```

### Base de Datos

Asegúrate de tener MongoDB ejecutándose localmente o configura la URI de MongoDB Atlas en las variables de entorno.

## 🚀 Ejecución

```bash
# Desarrollo
npm run start:dev

# Producción
npm run build
npm run start:prod

# Debug
npm run start:debug
```

## 🧪 Testing

```bash
# Tests unitarios
npm run test

# Tests e2e
npm run test:e2e

# Coverage
npm run test:cov

# Tests en modo watch
npm run test:watch
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ npm install -g @nestjs/mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## 📚 Documentación API

Una vez que el servidor esté ejecutándose, puedes acceder a la documentación de Swagger en:

```
http://localhost:3003/api/v1/docs
```

## 🔗 Endpoints

### Pedidos

| Método | Endpoint | Descripción |
|--------|----------|-------------|
| POST | `/api/v1/orders` | Crear nuevo pedido |
| GET | `/api/v1/orders` | Listar pedidos con filtros |
| GET | `/api/v1/orders/:id` | Obtener detalles de un pedido |
| PATCH | `/api/v1/orders/:id/cancel` | Cancelar un pedido |
| GET | `/api/v1/orders/stats` | Obtener estadísticas de pedidos |

### Headers Requeridos

Todos los endpoints requieren el header `x-user-id` con el ID del usuario.

```
x-user-id: 507f1f77bcf86cd799439011
```

## 📊 Estructura de Datos

### Pedido (Order)

```typescript
{
  cliente_id: string,
  productos: [
    {
      producto_id: string,
      nombre: string,
      cantidad: number,
      precio: number
    }
  ],
  total: number,
  estado: 'pendiente' | 'procesando' | 'enviado' | 'entregado' | 'cancelado',
  direccion_entrega: string,
  metodo_pago: string,
  fecha_pedido: Date,
  fecha_entrega_estimada: Date,
  notas?: string
}
```

## 📁 Estructura del Proyecto

```
src/
├── common/                 # Filtros, interceptors y utilidades comunes
│   ├── filters/           # Filtros de excepción
│   └── interceptors/      # Interceptors globales
├── config/                # Configuraciones de la aplicación
├── orders/                # Módulo de pedidos
│   ├── dto/              # Data Transfer Objects
│   ├── schemas/          # Esquemas de MongoDB
│   ├── orders.controller.ts
│   ├── orders.service.ts
│   └── orders.module.ts
├── app.module.ts
└── main.ts
```

## 🔧 Desarrollo

### Agregar nuevas funcionalidades

1. Crear DTOs en `src/orders/dto/`
2. Actualizar el servicio en `src/orders/orders.service.ts`
3. Agregar endpoints en `src/orders/orders.controller.ts`
4. Documentar con decoradores de Swagger

### Comunicación con otros microservicios

El microservicio está preparado para comunicarse con otros servicios a través de HTTP. Para implementar comunicación:

1. Instalar `@nestjs/axios` para HTTP clients
2. Crear servicios de integración en `src/integrations/`
3. Configurar URLs de otros microservicios en variables de entorno

## 📝 Licencia

Este proyecto está bajo la Licencia MIT.
