import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IOrder, IOrderDocument, IOrderRepository } from '../interfaces/order.interface';
import { Order } from '../schemas/order.schema';

@Injectable()
export class OrderRepository implements IOrderRepository {
  constructor(
    @InjectModel(Order.name) private orderModel: Model<IOrderDocument>,
  ) {}

  async create(order: IOrder): Promise<IOrder> {
    const newOrder = new this.orderModel(order);
    const savedOrder = await newOrder.save();
    return this.mapToOrder(savedOrder);
  }

  async findById(id: string): Promise<IOrder | null> {
    const order = await this.orderModel.findById(id).exec();
    return order ? this.mapToOrder(order) : null;
  }

  async findByUserId(userId: string, query: any): Promise<[IOrder[], number]> {
    const { page = 1, limit = 10, status, deliveryMethod, startDate, endDate } = query;
    const skip = (page - 1) * limit;
    
    const filter: any = { userId };
    
    if (status) filter.status = status;
    if (deliveryMethod) filter.deliveryMethod = deliveryMethod;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }
    
    const [orders, total] = await Promise.all([
      this.orderModel.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.orderModel.countDocuments(filter).exec(),
    ]);
    
    return [orders.map(order => this.mapToOrder(order)), total];
  }

  async findByStoreId(storeId: string, query: any): Promise<[IOrder[], number]> {
    const { page = 1, limit = 10, status } = query;
    const skip = (page - 1) * limit;
    
    const filter: any = { 'items.storeId': storeId };
    if (status) filter.status = status;
    
    const [orders, total] = await Promise.all([
      this.orderModel.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.orderModel.countDocuments(filter).exec(),
    ]);
    
    return [orders.map(order => this.mapToOrder(order)), total];
  }

  async update(id: string, data: Partial<IOrder>): Promise<IOrder | null> {
    const updatedOrder = await this.orderModel.findByIdAndUpdate(
      id,
      { ...data, updatedAt: new Date() },
      { new: true }
    ).exec();
    
    return updatedOrder ? this.mapToOrder(updatedOrder) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.orderModel.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  async countByStatus(filter: any): Promise<Record<string, number>> {
    const baseFilter = { ...filter };
    
    const [total, pending, confirmed, preparing, readyForPickup, pickedUp, inTransit, delivered, cancelled] = 
      await Promise.all([
        this.orderModel.countDocuments(baseFilter).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'PENDING' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'CONFIRMED' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'PREPARING' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'READY_FOR_PICKUP' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'PICKED_UP' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'IN_TRANSIT' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'DELIVERED' }).exec(),
        this.orderModel.countDocuments({ ...baseFilter, status: 'CANCELLED' }).exec(),
      ]);
    
    return {
      total,
      pending,
      confirmed,
      preparing,
      readyForPickup,
      pickedUp,
      inTransit,
      delivered,
      cancelled,
    };
  }

  async calculateTotalSpent(filter: any): Promise<number> {
    const result = await this.orderModel.aggregate([
      { $match: filter },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]).exec();
    
    return result.length > 0 ? result[0].total : 0;
  }

  private mapToOrder(document: IOrderDocument): IOrder {
    const order = document.toObject();
    return {
      ...order,
      id: order._id.toString(),
    };
  }
}