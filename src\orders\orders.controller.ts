import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  Headers,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiHeader,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { OrdersService } from './services/orders.service';
import { OrderStatus } from './enums/order.enum';
import {
  CreateOrderDto,
  QueryOrdersDto,
  QueryStoreOrdersDto,
  OrderResponseDto,
  PaginatedOrdersResponseDto,
  OrderStatsResponseDto,
  CancelOrderResponseDto,
} from './dto/order.dto';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Crear nuevo pedido',
    description: 'Crea un nuevo pedido desde el carrito o con productos específicos'
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario',
    required: true,
  })
  @ApiResponse({
    status: 201,
    description: 'Pedido creado exitosamente',
    type: OrderResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Datos de entrada inválidos, header x-user-id requerido, carrito vacío o fecha de entrega inválida',
  })
  async create(
    @Body() createOrderDto: CreateOrderDto,
    @Headers('x-user-id') userId: string,
  ): Promise<OrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.create(createOrderDto, userId);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar pedidos del usuario',
    description: 'Obtiene la lista de pedidos del usuario con filtros y paginación'
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de pedidos obtenida exitosamente',
    type: PaginatedOrdersResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido',
  })
  async findAll(
    @Query() queryDto: QueryOrdersDto,
    @Headers('x-user-id') userId: string,
  ): Promise<PaginatedOrdersResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.findAll(queryDto, userId);
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Obtener estadísticas de pedidos',
    description: 'Obtiene estadísticas básicas de los pedidos del usuario'
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estadísticas obtenidas exitosamente',
    type: OrderStatsResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido',
  })
  async getStats(
    @Headers('x-user-id') userId: string,
  ): Promise<OrderStatsResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.getStats(userId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obtener detalles de un pedido',
    description: 'Obtiene los detalles completos de un pedido específico'
  })
  @ApiParam({
    name: 'id',
    description: 'ID único del pedido',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Detalles del pedido obtenidos exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido',
  })
  async findOne(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<OrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.findOne(id, userId);
  }

  @Patch(':id/cancel')
  @ApiOperation({
    summary: 'Cancelar un pedido',
    description: 'Cancela un pedido que esté en estado pendiente'
  })
  @ApiParam({
    name: 'id',
    description: 'ID único del pedido a cancelar',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Pedido cancelado exitosamente',
    type: CancelOrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o el pedido no puede ser cancelado (no está en estado pendiente)',
  })
  async cancel(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<CancelOrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.cancel(id, userId);
  }

  @Patch(':id/assign-delivery')
  @ApiOperation({
    summary: 'Asignar repartidor a un pedido',
    description: 'Asigna un repartidor a un pedido existente'
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario (debe ser un repartidor)',
    required: true,
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido a asignar',
  })
  @ApiResponse({
    status: 200,
    description: 'Repartidor asignado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'El pedido ya tiene un repartidor asignado o no está en estado válido',
  })
  async assignDelivery(
    @Param('id') id: string,
    @Headers('x-user-id') deliveryId: string,
  ): Promise<OrderResponseDto> {
    if (!deliveryId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.assignDelivery(id, deliveryId);
  }

  @Get('store/:storeId')
  @ApiOperation({
    summary: 'Listar pedidos por tienda',
    description: 'Obtiene la lista de pedidos de una tienda específica con filtros y paginación'
  })
  @ApiParam({
    name: 'storeId',
    description: 'ID de la tienda',
    example: '681d702ab18e12c50e8e02ce',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de pedidos obtenida exitosamente',
    type: PaginatedOrdersResponseDto,
  })
  async findByStoreId(
    @Param('storeId') storeId: string,
    @Query() queryDto: Omit<QueryStoreOrdersDto, 'storeId'>,
  ): Promise<PaginatedOrdersResponseDto> {
    const fullQueryDto = {
      ...queryDto,
      storeId,
    } as QueryStoreOrdersDto;
    
    return this.ordersService.findByStoreId(fullQueryDto);
  }

  @Get('store/:storeId/stats')
  @ApiOperation({
    summary: 'Obtener estadísticas de pedidos por tienda',
    description: 'Obtiene estadísticas básicas de los pedidos de una tienda específica'
  })
  @ApiParam({
    name: 'storeId',
    description: 'ID de la tienda',
    example: '681d702ab18e12c50e8e02ce',
  })
  @ApiResponse({
    status: 200,
    description: 'Estadísticas obtenidas exitosamente',
    type: OrderStatsResponseDto,
  })
  async getStoreStats(
    @Param('storeId') storeId: string,
  ): Promise<OrderStatsResponseDto> {
    return this.ordersService.getStoreStats(storeId);
  }

  // Endpoints para la tienda
  @Patch(':id/store/confirm')
  @ApiOperation({
    summary: 'Confirmar pedido (Tienda)',
    description: 'La tienda confirma que ha recibido el pedido y lo procesará'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario de la tienda',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Pedido confirmado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async confirmOrderByStore(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<OrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.CONFIRMED, userId, 'store');
  }

  @Patch(':id/store/prepare')
  @ApiOperation({
    summary: 'Marcar pedido en preparación (Tienda)',
    description: 'La tienda marca que ha comenzado a preparar el pedido'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario de la tienda',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estado del pedido actualizado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async prepareOrderByStore(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<OrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.PREPARING, userId, 'store');
  }

  @Patch(':id/store/ready')
  @ApiOperation({
    summary: 'Marcar pedido listo para recoger (Tienda)',
    description: 'La tienda marca que el pedido está listo para ser recogido'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del usuario de la tienda',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estado del pedido actualizado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async readyOrderByStore(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<OrderResponseDto> {
    if (!userId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.READY_FOR_PICKUP, userId, 'store');
  }

  // Endpoints para el delivery
  @Patch(':id/delivery/pickup')
  @ApiOperation({
    summary: 'Marcar pedido como recogido (Delivery)',
    description: 'El repartidor marca que ha recogido el pedido de la tienda'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del repartidor',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estado del pedido actualizado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async pickupOrderByDelivery(
    @Param('id') id: string,
    @Headers('x-user-id') deliveryId: string,
  ): Promise<OrderResponseDto> {
    if (!deliveryId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.PICKED_UP, deliveryId, 'delivery');
  }

  @Patch(':id/delivery/in-transit')
  @ApiOperation({
    summary: 'Marcar pedido en tránsito (Delivery)',
    description: 'El repartidor marca que el pedido está en camino al cliente'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del repartidor',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estado del pedido actualizado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async inTransitOrderByDelivery(
    @Param('id') id: string,
    @Headers('x-user-id') deliveryId: string,
  ): Promise<OrderResponseDto> {
    if (!deliveryId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.IN_TRANSIT, deliveryId, 'delivery');
  }

  @Patch(':id/delivery/complete')
  @ApiOperation({
    summary: 'Marcar pedido como entregado (Delivery)',
    description: 'El repartidor marca que ha entregado el pedido al cliente'
  })
  @ApiParam({
    name: 'id',
    description: 'ID del pedido',
  })
  @ApiHeader({
    name: 'x-user-id',
    description: 'ID del repartidor',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Estado del pedido actualizado exitosamente',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Pedido no encontrado',
  })
  @ApiBadRequestResponse({
    description: 'Header x-user-id es requerido o transición de estado inválida',
  })
  async completeOrderByDelivery(
    @Param('id') id: string,
    @Headers('x-user-id') deliveryId: string,
  ): Promise<OrderResponseDto> {
    if (!deliveryId) {
      throw new BadRequestException('Header x-user-id es requerido');
    }
    return this.ordersService.updateOrderStatus(id, OrderStatus.DELIVERED, deliveryId, 'delivery');
  }
}




