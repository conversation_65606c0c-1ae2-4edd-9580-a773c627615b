# Application Configuration
PORT=3003
NODE_ENV=development
API_PREFIX=api/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/purchasing-microservice

# Microservices URLs
STORE_MICROSERVICE_URL=http://localhost:3001
DELIVERY_MICROSERVICE_URL=http://localhost:3002

# Optional: MongoDB Atlas connection string example
# MONGODB_URI=mongodb+srv://username:<EMAIL>/purchasing-microservice?retryWrites=true&w=majority
 k