import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsDate,
  ValidateNested,
  IsArray,
  Min,
  IsUrl,
  IsEmail,
  IsPhoneNumber,
  IsInt,
} from 'class-validator';
import { OrderStatus, DeliveryMethod } from '../enums/order.enum';
import { IOrderItem, ICustomerInfo, IOrder } from '../interfaces/order.interface';

// Request DTOs
export class OrderItemDto implements IOrderItem {
  @ApiProperty({ description: 'ID del producto' })
  @IsString()
  productId: string;

  @ApiProperty({ description: 'Nombre del producto' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Precio unitario del producto' })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: 'Cantidad del producto' })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ description: 'ID de la tienda' })
  @IsString()
  storeId: string;

  @ApiProperty({ description: 'Nombre de la tienda' })
  @IsString()
  storeName: string;

  @ApiPropertyOptional({ description: 'URL de la imagen del producto' })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;
}

export class CustomerInfoDto implements ICustomerInfo {
  @ApiProperty({ description: 'Nombre del cliente' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Email del cliente' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ description: 'Teléfono del cliente' })
  @IsString()
  phone: string;

  @ApiPropertyOptional({ description: 'Dirección del cliente' })
  @IsOptional()
  @IsString()
  address?: string;
}

export class CreateOrderDto {
  @ApiProperty({
    description: 'Productos en el pedido',
    type: [OrderItemDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Información del cliente',
    type: CustomerInfoDto
  })
  @ValidateNested()
  @Type(() => CustomerInfoDto)
  customer: CustomerInfoDto;

  @ApiProperty({ description: 'Total del pedido' })
  @IsNumber()
  @Min(0)
  total: number;

  @ApiProperty({
    description: 'Método de entrega',
    enum: DeliveryMethod
  })
  @IsEnum(DeliveryMethod)
  deliveryMethod: DeliveryMethod;

  @ApiPropertyOptional({ description: 'Dirección de entrega' })
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @ApiPropertyOptional({ description: 'Fecha de entrega programada' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deliveryDate?: Date;

  @ApiPropertyOptional({ description: 'Notas adicionales del pedido' })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateOrderDto extends PartialType(CreateOrderDto) {}

export class QueryOrdersDto {
  @ApiPropertyOptional({ description: 'Página actual', default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Elementos por página', default: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Filtrar por estado', enum: OrderStatus })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({ description: 'Filtrar por método de entrega', enum: DeliveryMethod })
  @IsOptional()
  @IsEnum(DeliveryMethod)
  deliveryMethod?: DeliveryMethod;

  @ApiPropertyOptional({ description: 'Fecha de inicio para filtrar' })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'Fecha de fin para filtrar' })
  @IsOptional()
  @IsString()
  endDate?: string;
}

export class QueryStoreOrdersDto extends QueryOrdersDto {
  @ApiProperty({ description: 'ID de la tienda' })
  @IsString()
  storeId: string;
}

// Response DTOs
export class OrderResponseDto implements IOrder {
  @ApiProperty({ description: 'ID del pedido' })
  id: string;

  @ApiProperty({ description: 'ID del usuario que realizó el pedido' })
  userId: string;

  @ApiPropertyOptional({ description: 'ID del repartidor asignado' })
  deliveryId?: string;

  @ApiProperty({
    description: 'Productos en el pedido',
    type: [OrderItemDto]
  })
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Información del cliente',
    type: CustomerInfoDto
  })
  customer: CustomerInfoDto;

  @ApiProperty({ description: 'Total del pedido' })
  total: number;

  @ApiProperty({
    description: 'Estado del pedido',
    enum: OrderStatus
  })
  status: OrderStatus;

  @ApiProperty({
    description: 'Método de entrega',
    enum: DeliveryMethod
  })
  deliveryMethod: DeliveryMethod;

  @ApiPropertyOptional({ description: 'Dirección de entrega' })
  deliveryAddress?: string;

  @ApiPropertyOptional({ description: 'Fecha de entrega programada' })
  deliveryDate?: Date;

  @ApiPropertyOptional({ description: 'Notas adicionales del pedido' })
  notes?: string;

  @ApiProperty({ description: 'Fecha de creación' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de última actualización' })
  updatedAt: Date;
}

export class PaginationDto {
  @ApiProperty({ description: 'Total de elementos' })
  total: number;

  @ApiProperty({ description: 'Página actual' })
  page: number;

  @ApiProperty({ description: 'Elementos por página' })
  limit: number;

  @ApiProperty({ description: 'Total de páginas' })
  pages: number;
}

export class PaginatedOrdersResponseDto {
  @ApiProperty({
    description: 'Lista de pedidos',
    type: [OrderResponseDto]
  })
  data: OrderResponseDto[];

  @ApiProperty({
    description: 'Información de paginación',
    type: PaginationDto
  })
  pagination: PaginationDto;
}

export class OrderStatsResponseDto {
  @ApiProperty({ description: 'Total de pedidos' })
  total: number;

  @ApiProperty({ description: 'Pedidos pendientes' })
  pending: number;

  @ApiProperty({ description: 'Pedidos confirmados' })
  confirmed: number;

  @ApiProperty({ description: 'Pedidos en preparación' })
  preparing: number;

  @ApiProperty({ description: 'Pedidos listos para recoger' })
  readyForPickup: number;

  @ApiProperty({ description: 'Pedidos recogidos' })
  pickedUp: number;

  @ApiProperty({ description: 'Pedidos en tránsito' })
  inTransit: number;

  @ApiProperty({ description: 'Pedidos entregados' })
  delivered: number;

  @ApiProperty({ description: 'Pedidos cancelados' })
  cancelled: number;

  @ApiProperty({ description: 'Total gastado/ingresos' })
  totalSpent: number;
}

export class CancelOrderResponseDto {
  @ApiProperty({ description: 'ID del pedido' })
  id: string;

  @ApiProperty({ description: 'Mensaje de confirmación' })
  message: string;

  @ApiProperty({
    description: 'Estado del pedido',
    enum: OrderStatus,
    example: OrderStatus.CANCELLED
  })
  status: OrderStatus;
}